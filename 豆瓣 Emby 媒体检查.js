// ==UserScript==
// @name         豆瓣 Emby 媒体检查
// @namespace    http://tampermonkey.net/
// @version      4.4.8 Complete // Updated version
// @description  在豆瓣电影页面检查您的 Emby 服务器中是否存在该媒体。浮动按钮样式，支持站点启用/禁用设置。
// <AUTHOR> & Optimized
// @match        https://movie.douban.com/subject/*
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_info
// @connect      embyHost
// @require      https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js
// @key          提醒：使用此脚本前，请务必修改下方代码中的 embyHost 和 embyApiKey 为您自己的 Emby 服务器地址和API密钥。
// @key          同时，您可能需要根据您的 embyHost 修改上面一行 "@connect embyHost" 中的 "embyHost" 为您的实际主机名 (例如 @connect my.emby.server.com 或 @connect *************)，以便油猴插件授权网络请求。
// ==/UserScript==

(function () {
    'use strict';

    // ==================== 配置区域 ====================
    // Emby 配置现在通过设置界面管理
    let embyHost = GM_getValue('emby_host', '');
    let embyApiKey = GM_getValue('emby_api_key', '');

    // 默认站点配置
    const DEFAULT_SITES_CONFIG = {
        mteam: { name: '馒头', emoji: '🥟', url: 'https://kp.m-team.cc/browse?keyword={title}', searchType: 'title', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', order: 1 },
        audiences: { name: '观众', emoji: '👥', url: 'https://audiences.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', order: 2 },
        hhanclub: { name: '憨憨', emoji: '😊', url: 'https://hhanclub.top/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', order: 3 },
        hdsky: { name: '天空', emoji: '☁️', url: 'https://hdsky.me/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', order: 4 },
        chdbits: { name: '彩虹岛', emoji: '🌈', url: 'https://chdbits.co/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', order: 5 },
        ttg: { name: '听听歌', emoji: '🎵', url: 'https://totheglory.im/browse.php?c=M&search_field={title}', searchType: 'title', gradient: 'linear-gradient(135deg, #a8caba 0%, #5d4e75 100%)', order: 6 },
        ourbits: { name: '我堡', emoji: '🏰', url: 'https://ourbits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)', order: 7 },
        hddolby: { name: '杜比', emoji: '🔊', url: 'https://www.hddolby.com/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)', order: 8 },
        ssd: { name: '春天', emoji: '🌸', url: 'https://springsunday.net/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%)', order: 9 },
        ubits: { name: '你堡', emoji: '🏯', url: 'https://ubits.club/torrents.php?search={imdb}&search_area=4', searchType: 'imdb', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', order: 10 }
    };

    // 获取站点配置（包括自定义站点）
    function getSitesConfig() {
        const customSites = GM_getValue('custom_sites', {});
        const allSites = { ...DEFAULT_SITES_CONFIG, ...customSites };
        const siteOrder = GM_getValue('site_order', []);

        // 如果有自定义排序，按排序返回
        if (siteOrder.length > 0) {
            const orderedSites = {};
            siteOrder.forEach(siteKey => {
                if (allSites[siteKey]) {
                    orderedSites[siteKey] = allSites[siteKey];
                }
            });
            // 添加不在排序中的新站点
            Object.keys(allSites).forEach(siteKey => {
                if (!orderedSites[siteKey]) {
                    orderedSites[siteKey] = allSites[siteKey];
                }
            });
            return orderedSites;
        }

        return allSites;
    }

    const CONFIG = {
        cacheTTL: 10 * 60 * 1000,
        requestTimeout: 15000,
        maxRetries: 2,
        retryDelay: 1200,
        settingsKey: "doubanEmbyScript_enabledSites_v4.4.8", // Updated version in key
        settingsUsedKey: "doubanEmbyScript_settingsUsed_v4.4.8", // Updated version in key
        colors: {
            // MODIFIED: New color for "已入库"
            success: 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)', // Clean Green
            warning: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            error: 'linear-gradient(135deg, #fc466b 0%, #3f5efb 100%)',
            loading: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
            // MODIFIED: New color for "TMDB"
            primaryAction: 'linear-gradient(135deg, #0288D1 0%, #26C6DA 100%)', // Cool Blue to Teal
            // "Emby搜索" will use this for distinction
            secondaryAction: 'linear-gradient(135deg, #4E73DF 0%, #7693F5 100%)', // A modern, slightly softer blue
            settingsButton: 'linear-gradient(135deg, #606c88 0%, #3f4c6b 100%)',
            settingsButtonIconOnly: 'linear-gradient(135deg, #788698 0%, #5c687e 100%)',
            textOnColor: '#ffffff',
            textShadowLightBg: '0 0 2px rgba(0,0,0,0.6), 0 0 1px rgba(0,0,0,0.6)',
            modalBackgroundOverlay: 'rgba(0, 0, 0, 0.7)',
            modalContentBg: '#ffffff',
            textPrimaryModal: '#1a202c',
            textSecondaryModal: '#4a5568',
            borderModal: '#d1d5db',
        },
        spacing: { xs: '4px', sm: '8px', md: '10px', lg: '12px', xl: '16px' },
        borderRadius: { button: '50px', modal: '10px' },
        fontSize: { sm: '13px', md: '14px', lg: '15px' }
    };

    let enabledSites = {};
    let settingsButtonUsed = false;

    // ==================== 样式定义 ====================
    function injectStyles() {
        GM_addStyle(`
            #emby-script-container {
                padding: ${CONFIG.spacing.sm} 0;
                margin: ${CONFIG.spacing.md} 0;
                position: relative;
            }
            body.subject_page #content .article > h1 + #emby-script-container {
                 margin-left: -15px; /* Adjust this! */
            }
            #emby-script-button-bar {
                display: flex; flex-wrap: wrap; gap: ${CONFIG.spacing.sm};
                align-items: center; margin-bottom: ${CONFIG.spacing.sm};
            }
            #emby-status-container { margin-right: ${CONFIG.spacing.xs}; }
            .es-button {
                display: inline-flex; align-items: center; justify-content: center;
                height: 34px; padding: 0 18px;
                border-radius: ${CONFIG.borderRadius.button};
                font-size: ${CONFIG.fontSize.sm}; font-weight: 500;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                color: ${CONFIG.colors.textOnColor} !important; text-decoration: none !important;
                border: none; position: relative; overflow: hidden; cursor: pointer;
                gap: 6px; white-space: nowrap; line-height: 34px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .es-button:hover {
                transform: translateY(-2px) scale(1.02);
                box-shadow: 0 4px 10px rgba(0,0,0,0.2), 0 6px 12px rgba(0,0,0,0.15);
            }
            .es-button:active { transform: translateY(-1px) scale(0.99); transition-duration: 0.1s; }
            .es-button::before {
                content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
                transition: left 0.4s cubic-bezier(0.4,0,0.2,1); border-radius: ${CONFIG.borderRadius.button};
            }
            .es-button:hover::before { left: 100%; }
            .es-button.status-success { background: ${CONFIG.colors.success}; }
            .es-button.status-warning { background: ${CONFIG.colors.warning}; }
            .es-button.status-error { background: ${CONFIG.colors.error}; }
            .es-button.status-loading { background: ${CONFIG.colors.loading}; cursor: default; }
            .es-button.status-loading:hover { transform: none; box-shadow: 0 2px 5px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1); }
            .es-button.action-primary { background: ${CONFIG.colors.primaryAction}; }
            .es-button.action-secondary { background: ${CONFIG.colors.secondaryAction}; }
            .es-button.action-settings { background: ${CONFIG.colors.settingsButton}; }
            .es-button.action-settings.icon-only { background: ${CONFIG.colors.settingsButtonIconOnly}; padding: 0; width: 34px; min-width: 34px; }
            .es-button.action-settings.icon-only .settings-text { display: none; }
            .es-button.action-settings.icon-only .settings-icon-symbol { font-size: 1.2em; }
            .pt-sites-container {
                display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
                gap: ${CONFIG.spacing.sm}; max-height: 0; overflow: hidden;
                transition: all 0.35s cubic-bezier(0.4,0,0.2,1);
                opacity: 0; margin-top: 0; padding-top: 0;
            }
            .pt-sites-container.expanded {
                max-height: 130px; opacity: 1; margin-top: ${CONFIG.spacing.md};
                padding: ${CONFIG.spacing.xs} 0;
                overflow-y: auto; scrollbar-width: thin; scrollbar-color: rgba(0,0,0,0.1) transparent;
            }
            .pt-sites-container.expanded::-webkit-scrollbar { width: 4px; }
            .pt-sites-container.expanded::-webkit-scrollbar-track { background: transparent; }
            .pt-sites-container.expanded::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); border-radius: 10px; }
            .pt-site-item {
                cursor: grab; transition: all 0.3s ease;
                user-select: none; -webkit-user-select: none; -moz-user-select: none;
                position: relative;
            }
            .pt-site-item:hover {
                transform: scale(1.05) translateY(-2px); z-index: 100;
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            }
            .pt-site-item:active {
                cursor: grabbing;
            }
            .pt-site-item.sortable-ghost {
                opacity: 0.4 !important;
                transform: scale(0.95) !important;
                background: rgba(33, 150, 243, 0.1) !important;
                border: 2px dashed #2196f3 !important;
                pointer-events: none !important;
            }
            .pt-site-item.sortable-chosen {
                transform: scale(1.02) !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
                z-index: 1000 !important;
                opacity: 0.9 !important;
            }
            .pt-site-item.sortable-drag {
                transform: scale(1.05) !important;
                box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
                z-index: 1001 !important;
                opacity: 0.8 !important;
                pointer-events: none !important;
            }
            .pt-sites-container.expanded {
                user-select: none; -webkit-user-select: none; -moz-user-select: none;
            }
            /* 防止拖拽时选中文本 */
            .sortable-fallback {
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
            }
            .es-button.pt-site.light-gradient-text-shadow { text-shadow: ${CONFIG.colors.textShadowLightBg}; }
            .toggle-icon { transition: all 0.3s cubic-bezier(0.4,0,0.2,1); display: inline-block; }
            .toggle-icon.rotated { transform: rotate(180deg) scale(1.05); }
            .loading-spinner {
                width: 16px; height: 16px; border: 2px solid rgba(255,255,255,0.25);
                border-radius: 50%; border-top-color: ${CONFIG.colors.textOnColor};
                animation: es-spin 0.8s linear infinite;
            }
            @keyframes es-spin { to { transform: rotate(360deg); } }
            #es-settings-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: ${CONFIG.colors.modalBackgroundOverlay}; display: none; align-items: center; justify-content: center; z-index: 10000; }
            #es-settings-modal.active { display: flex; }
            .es-settings-content {
                background-color: ${CONFIG.colors.modalContentBg};
                padding: ${CONFIG.spacing.lg}; border-radius: ${CONFIG.borderRadius.modal};
                border: 1px solid ${CONFIG.colors.borderModal};
                box-shadow: 0 10px 25px rgba(0,0,0,0.25);
                width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;
            }
            .es-settings-tabs {
                display: flex; margin-bottom: ${CONFIG.spacing.md}; border-bottom: 2px solid ${CONFIG.colors.borderModal};
            }
            .es-settings-tab {
                padding: ${CONFIG.spacing.sm} ${CONFIG.spacing.md}; cursor: pointer; border: none; background: none;
                font-size: ${CONFIG.fontSize.md}; color: ${CONFIG.colors.textSecondaryModal}; border-bottom: 2px solid transparent;
                transition: all 0.2s ease;
            }
            .es-settings-tab.active {
                color: ${CONFIG.colors.textPrimaryModal}; border-bottom-color: ${CONFIG.colors.primaryAction.split('(')[1].split(',')[1].trim()};
            }
            .es-settings-tab-content {
                display: none;
            }
            .es-settings-tab-content.active {
                display: block;
            }
            .es-config-section {
                margin-bottom: ${CONFIG.spacing.lg}; padding: ${CONFIG.spacing.md};
                border: 1px solid ${CONFIG.colors.borderModal}; border-radius: ${CONFIG.borderRadius.modal};
                background: #f8f9fa;
            }
            .es-config-section h4 {
                margin: 0 0 ${CONFIG.spacing.sm} 0; color: ${CONFIG.colors.textPrimaryModal};
            }
            .es-config-input {
                width: 100%; padding: ${CONFIG.spacing.sm}; border: 1px solid ${CONFIG.colors.borderModal};
                border-radius: 4px; font-size: ${CONFIG.fontSize.md}; margin-bottom: ${CONFIG.spacing.sm};
            }
            .es-add-site-form {
                display: grid; grid-template-columns: 1fr 1fr; gap: ${CONFIG.spacing.sm}; margin-top: ${CONFIG.spacing.sm};
            }
            .es-add-site-form input, .es-add-site-form select {
                padding: ${CONFIG.spacing.sm}; border: 1px solid ${CONFIG.colors.borderModal}; border-radius: 4px;
            }
            .es-add-site-btn {
                grid-column: span 2; background: ${CONFIG.colors.primaryAction}; color: white; border: none;
                padding: ${CONFIG.spacing.sm}; border-radius: 4px; cursor: pointer; font-size: ${CONFIG.fontSize.md};
            }
            .es-settings-content h3 { margin-top:0; margin-bottom:${CONFIG.spacing.md}; font-size:${CONFIG.fontSize.lg}; color:${CONFIG.colors.textPrimaryModal}; }
            .es-settings-site-toggle {
                display: flex; align-items: center; justify-content: space-between;
                padding: ${CONFIG.spacing.sm}; margin-bottom: ${CONFIG.spacing.xs};
                border: 1px solid ${CONFIG.colors.borderModal}; border-radius: 8px;
                background: white; transition: all 0.3s ease;
                user-select: none; -webkit-user-select: none; -moz-user-select: none;
                position: relative; overflow: hidden;
            }
            .es-settings-site-toggle:hover {
                background: #f8f9fa; transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }
            .es-settings-site-toggle.sortable-ghost {
                opacity: 0.4 !important;
                transform: scale(0.95) !important;
                background: #e3f2fd !important;
                border: 2px dashed #2196f3 !important;
                pointer-events: none !important;
            }
            .es-settings-site-toggle.sortable-chosen {
                transform: scale(1.02) !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
                z-index: 1000 !important;
                background: #fff3e0 !important;
                opacity: 0.9 !important;
            }
            .es-settings-site-toggle.sortable-drag {
                transform: scale(1.05) !important;
                box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
                background: #fff !important;
                border: 2px solid #4caf50 !important;
                z-index: 1001 !important;
                opacity: 0.8 !important;
                pointer-events: none !important;
            }
            .es-settings-site-toggle label {
                font-size: ${CONFIG.fontSize.md}; color: ${CONFIG.colors.textSecondaryModal};
                cursor: default; display: flex; align-items: center; gap: ${CONFIG.spacing.sm};
                flex: 1; pointer-events: none; user-select: none;
            }
            .es-settings-site-toggle input[type="checkbox"] {
                width: 18px; height: 18px; cursor: pointer; pointer-events: auto;
                accent-color: ${CONFIG.colors.primaryAction.split('(')[1].split(',')[1].trim()};
            }
            .es-site-delete-btn {
                background: #dc3545; color: white; border: none; padding: 4px 8px;
                border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: ${CONFIG.spacing.sm};
                pointer-events: auto; user-select: none;
            }
            .es-drag-handle {
                cursor: grab; color: #666; margin-right: ${CONFIG.spacing.sm};
                font-size: 18px; user-select: none; padding: 4px;
                border-radius: 4px; transition: all 0.2s ease;
                display: flex; align-items: center; justify-content: center;
                width: 24px; height: 24px;
            }
            .es-drag-handle:hover {
                background: rgba(0,0,0,0.1); color: #333;
            }
            .es-drag-handle:active {
                cursor: grabbing; background: rgba(0,0,0,0.2);
            }
            .es-sortable-list {
                user-select: none; -webkit-user-select: none; -moz-user-select: none;
            }
            .es-settings-actions { margin-top: ${CONFIG.spacing.lg}; text-align: right; }
            #es-settings-close-btn { background: ${CONFIG.colors.primaryAction}; }

            /* 全局拖拽样式 */
            .dragging-active {
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
            }

            /* 防止拖拽时选中文本和干扰 */
            .sortable-fallback {
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                opacity: 0.8 !important;
                transform: rotate(5deg) !important;
                box-shadow: 0 8px 25px rgba(0,0,0,0.4) !important;
                z-index: 9999 !important;
                pointer-events: none !important;
            }

            /* 确保拖拽元素不会复制 */
            .sortable-ghost {
                visibility: hidden !important;
            }

            /* 拖拽时的容器样式 */
            .es-sortable-list.sortable-drag-active,
            .pt-sites-container.sortable-drag-active {
                overflow: visible !important;
            }
        `);
    }

    // ==================== Site Settings Management ====================
    function loadSiteSettings() {
        const storedSettings = GM_getValue(CONFIG.settingsKey, null);
        const sitesConfig = getSitesConfig();
        if (storedSettings && typeof storedSettings === 'object') {
            enabledSites = storedSettings;
            for (const siteKey in sitesConfig) {
                if (typeof enabledSites[siteKey] === 'undefined') {
                    enabledSites[siteKey] = true;
                }
            }
        } else {
            for (const siteKey in sitesConfig) {
                enabledSites[siteKey] = true;
            }
        }
    }

    function saveSiteSettings() {
        GM_setValue(CONFIG.settingsKey, enabledSites);
    }

    function saveSiteOrder(siteOrder) {
        GM_setValue('site_order', siteOrder);
    }



    function saveCustomSites(customSites) {
        GM_setValue('custom_sites', customSites);
    }

    function getCustomSites() {
        return GM_getValue('custom_sites', {});
    }

    function saveEmbyConfig(host, apiKey) {
        GM_setValue('emby_host', host);
        GM_setValue('emby_api_key', apiKey);
        embyHost = host;
        embyApiKey = apiKey;
    }

    function createSettingsModal(movieInfo) {
        let modal = document.getElementById('es-settings-modal');
        if (modal) {
            // 销毁已存在的Sortable实例
            const existingSitesList = modal.querySelector('#es-site-toggles-list');
            if (existingSitesList && existingSitesList.sortableInstance) {
                existingSitesList.sortableInstance.destroy();
            }
            modal.remove();
        }
        modal = document.createElement('div');
        modal.id = 'es-settings-modal';

        // 生成 Emby 配置表单
        const embyConfigHTML = `
            <div class="es-config-section">
                <h4>🎬 Emby 服务器配置</h4>
                <input type="text" id="emby-host-input" class="es-config-input"
                       placeholder="Emby 服务器地址 (如: http://*************:8096)"
                       value="${embyHost}">
                <input type="text" id="emby-apikey-input" class="es-config-input"
                       placeholder="Emby API Key"
                       value="${embyApiKey}">
            </div>`;

        // 生成站点列表
        let siteTogglesHTML = '';
        const sitesConfig = getSitesConfig();
        for (const [siteKey, siteData] of Object.entries(sitesConfig)) {
            const isChecked = enabledSites[siteKey] === true;
            const isCustom = !DEFAULT_SITES_CONFIG[siteKey];
            const deleteBtn = isCustom ? `<button class="es-site-delete-btn" data-sitekey="${siteKey}">删除</button>` : '';
            siteTogglesHTML += `
                <div class="es-settings-site-toggle" data-sitekey="${siteKey}">
                    <span class="es-drag-handle">⋮⋮</span>
                    <label for="toggle-${siteKey}">${siteData.emoji} ${siteData.name}</label>
                    <div>
                        <input type="checkbox" id="toggle-${siteKey}" data-sitekey="${siteKey}" ${isChecked ? 'checked' : ''}>
                        ${deleteBtn}
                    </div>
                </div>`;
        }

        // 生成自定义站点添加表单
        const addSiteHTML = `
            <div class="es-config-section">
                <h4>➕ 添加自定义站点</h4>
                <div class="es-add-site-form">
                    <input type="text" id="new-site-name" placeholder="站点名称">
                    <input type="text" id="new-site-emoji" placeholder="表情符号">
                    <input type="text" id="new-site-url" placeholder="搜索URL (使用 {title} 或 {imdb})">
                    <select id="new-site-search-type">
                        <option value="title">按标题搜索</option>
                        <option value="imdb">按IMDB搜索</option>
                    </select>
                    <input type="text" id="new-site-gradient" placeholder="渐变色 (可选)">
                    <button type="button" class="es-add-site-btn" id="add-site-btn">添加站点</button>
                </div>
            </div>`;

        modal.innerHTML = `
            <div class="es-settings-content">
                <div class="es-settings-tabs">
                    <button class="es-settings-tab active" data-tab="emby">Emby 配置</button>
                    <button class="es-settings-tab" data-tab="sites">站点管理</button>
                </div>

                <div class="es-settings-tab-content active" id="emby-tab">
                    ${embyConfigHTML}
                </div>

                <div class="es-settings-tab-content" id="sites-tab">
                    <h3>🎯 PT站点显示设置</h3>
                    <p style="color: #666; font-size: 13px; margin-bottom: 15px;">
                        <span style="color: #4caf50;">💡 提示：</span>拖拽左侧的 <span style="background: #f0f0f0; padding: 2px 4px; border-radius: 3px;">⋮⋮</span> 图标可调整显示顺序
                    </p>
                    <div id="es-site-toggles-list" class="es-sortable-list">${siteTogglesHTML}</div>
                    ${addSiteHTML}
                </div>

                <div class="es-settings-actions">
                    <button id="es-settings-close-btn" class="es-button">保存并关闭</button>
                </div>
            </div>`;
        document.body.appendChild(modal);
        // 标签页切换
        modal.querySelectorAll('.es-settings-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                modal.querySelectorAll('.es-settings-tab').forEach(t => t.classList.remove('active'));
                modal.querySelectorAll('.es-settings-tab-content').forEach(c => c.classList.remove('active'));
                tab.classList.add('active');
                modal.querySelector(`#${tab.dataset.tab}-tab`).classList.add('active');
            });
        });

        // 站点开关事件
        modal.querySelectorAll('#es-site-toggles-list input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                const siteKey = event.target.dataset.sitekey;
                enabledSites[siteKey] = event.target.checked;
                saveSiteSettings();
            });
        });

        // 站点删除事件
        modal.querySelectorAll('.es-site-delete-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const siteKey = event.target.dataset.sitekey;
                if (confirm(`确定要删除站点 "${getSitesConfig()[siteKey]?.name}" 吗？`)) {
                    const customSites = getCustomSites();
                    delete customSites[siteKey];
                    saveCustomSites(customSites);
                    delete enabledSites[siteKey];
                    saveSiteSettings();
                    createSettingsModal(movieInfo);
                    openSettingsModal();
                }
            });
        });

        // 添加自定义站点
        modal.querySelector('#add-site-btn').addEventListener('click', () => {
            const name = modal.querySelector('#new-site-name').value.trim();
            const emoji = modal.querySelector('#new-site-emoji').value.trim();
            const url = modal.querySelector('#new-site-url').value.trim();
            const searchType = modal.querySelector('#new-site-search-type').value;
            const gradient = modal.querySelector('#new-site-gradient').value.trim();

            if (!name || !url) {
                alert('请填写站点名称和搜索URL');
                return;
            }

            const siteKey = 'custom_' + Date.now();
            const customSites = getCustomSites();
            customSites[siteKey] = {
                name: name,
                emoji: emoji || '🌐',
                url: url,
                searchType: searchType,
                gradient: gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                order: Object.keys(getSitesConfig()).length + 1
            };

            saveCustomSites(customSites);
            enabledSites[siteKey] = true;
            saveSiteSettings();

            // 清空表单
            modal.querySelector('#new-site-name').value = '';
            modal.querySelector('#new-site-emoji').value = '';
            modal.querySelector('#new-site-url').value = '';
            modal.querySelector('#new-site-gradient').value = '';

            createSettingsModal(movieInfo);
            openSettingsModal();
        });

        // 初始化拖拽排序
        const sitesList = modal.querySelector('#es-site-toggles-list');
        if (window.Sortable && sitesList) {
            // 销毁已存在的Sortable实例
            if (sitesList.sortableInstance) {
                sitesList.sortableInstance.destroy();
            }

            sitesList.sortableInstance = Sortable.create(sitesList, {
                handle: '.es-drag-handle',
                animation: 150,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                forceFallback: false, // 改为false，使用原生拖拽
                fallbackTolerance: 0,
                scroll: true,
                scrollSensitivity: 30,
                scrollSpeed: 10,
                swapThreshold: 0.65,
                invertSwap: false,
                direction: 'vertical',
                onStart: function(evt) {
                    console.log('拖拽开始:', evt.item.dataset.sitekey);
                    document.body.classList.add('dragging-active');
                    // 不禁用指针事件，让拖拽正常工作
                },
                onEnd: function(evt) {
                    console.log('拖拽结束:', evt.item.dataset.sitekey, '新位置:', evt.newIndex);
                    document.body.classList.remove('dragging-active');

                    // 获取新的排序并保存
                    const newOrder = Array.from(sitesList.children).map(item => item.dataset.sitekey);
                    console.log('新排序:', newOrder);
                    saveSiteOrder(newOrder);

                    // 显示保存成功提示
                    const saveIndicator = document.createElement('div');
                    saveIndicator.textContent = '✅ 排序已保存';
                    saveIndicator.style.cssText = `
                        position: fixed; top: 20px; right: 20px; z-index: 10000;
                        background: #4caf50; color: white; padding: 8px 16px;
                        border-radius: 4px; font-size: 14px; box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                    `;
                    document.body.appendChild(saveIndicator);
                    setTimeout(() => saveIndicator.remove(), 2000);
                }
            });
        }

        const closeModalAndUpdateButton = () => {
            // 保存 Emby 配置
            const newHost = modal.querySelector('#emby-host-input').value.trim();
            const newApiKey = modal.querySelector('#emby-apikey-input').value.trim();
            if (newHost !== embyHost || newApiKey !== embyApiKey) {
                saveEmbyConfig(newHost, newApiKey);
            }

            modal.classList.remove('active');
            settingsButtonUsed = true;
            GM_setValue(CONFIG.settingsUsedKey, true);
            updateSettingsButtonAppearance();

            // 重新渲染界面
            insertUI(movieInfo);
            if (!document.getElementById('emby-status-container')?.firstChild?.classList.contains('status-loading')) {
                checkEmby(movieInfo).then(result => updateStatus(result.html));
            }
        };

        modal.querySelector('#es-settings-close-btn').addEventListener('click', closeModalAndUpdateButton);
        modal.addEventListener('click', (event) => {
            if (event.target.id === 'es-settings-modal') closeModalAndUpdateButton();
        });
    }

    function openSettingsModal() {
        const modal = document.getElementById('es-settings-modal');
        if (modal) modal.classList.add('active');
    }

    function updateSettingsButtonAppearance() {
        const settingsBtn = document.getElementById('es-open-settings-btn');
        if (settingsBtn && settingsButtonUsed) {
            settingsBtn.classList.add('icon-only');
            settingsBtn.title = "设置";
        }
    }

    // ==================== 工具函数 ====================
    function validateConfig() {
        // 重新获取最新的配置
        embyHost = GM_getValue('emby_host', '');
        embyApiKey = GM_getValue('emby_api_key', '');

        if (!embyHost || embyHost.trim() === "" || embyHost.includes("填写") || embyHost === "YOUR_EMBY_HOST" ||
            !embyApiKey || embyApiKey.trim() === "" || embyApiKey.includes("填写") || embyApiKey === "YOUR_EMBY_API_KEY") {
            console.error('[Emby Script] 配置未完成，请在设置中配置 Emby 服务器地址和 API 密钥。');
            return false;
        }
        return true;
    }

    function requestEmby(url) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: "GET", url, timeout: CONFIG.requestTimeout,
                onload: res => (res.status >= 200 && res.status < 300) ? resolve(res.responseText) : reject(new Error(`HTTP ${res.status}: ${res.statusText} for ${url}`)),
                onerror: (err) => reject(new Error(`Network Error for ${url}: ${err.error || 'Unknown'}`)),
                ontimeout: () => reject(new Error(`Request Timeout for ${url}`))
            });
        });
    }

    async function fetchWithRetry(url) {
        for (let attempt = 1; attempt <= CONFIG.maxRetries; attempt++) {
            try {
                return JSON.parse(await requestEmby(url));
            } catch (error) {
                console.warn(`[Emby Script] Request attempt ${attempt}/${CONFIG.maxRetries} failed for ${url}:`, error.message);
                if (attempt === CONFIG.maxRetries) {
                    console.error(`[Emby Script] All retries failed for ${url}.`);
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay * attempt));
            }
        }
        throw new Error("fetchWithRetry loop completed without success or error throw.");
    }

    function extractDoubanInfo() {
        const douban_id = window.location.href.match(/subject\/(\d+)/)?.[1] || '';
        let unititle = '';
        const titleSpan = document.querySelector("#content > h1 > span[property='v:itemreviewed']");
        if (titleSpan) {
            unititle = titleSpan.textContent.trim();
        } else {
            const firstSpan = document.querySelector("#content > h1 > span:first-child");
            if (firstSpan) {
                unititle = firstSpan.textContent.replace(/\s*\(\d{4}\)$/, "").trim();
            }
        }
        if (!unititle) {
            unititle = document.title.replace(/（.*/, "").replace(/\s*-\s*豆瓣.*/, "").trim();
        }
        let imdb_id = document.querySelector("#info")?.textContent.match(/IMDb:\s*(tt\d+)/i)?.[1] || "";
        console.log('[Emby Script] Extracted:', { douban_id, unititle, imdb_id });
        return { imdb_id, unititle, douban_id };
    }

    const CacheManager = {
        get(key) {
            try {
                const data = JSON.parse(sessionStorage.getItem(key));
                if (data && Date.now() - data.timestamp < CONFIG.cacheTTL) { return data.value; }
                if (data) sessionStorage.removeItem(key);
            } catch (e) { console.warn("Cache get error", e); }
            return null;
        },
        set(key, value) {
            try { sessionStorage.setItem(key, JSON.stringify({ value: value, timestamp: Date.now() })); }
            catch (e) { console.warn("Cache set error", e); }
        },
    };

    // ==================== UI 生成与渲染 ====================
    function generateSiteLinks(movieInfo) {
        let ptLinksHTML = '', externalLinksHTML = '', ptToggleButtonHTML = '', settingsButtonHTML = '';
        let enabledPtSiteCount = 0;
        settingsButtonHTML = `<button id="es-open-settings-btn" class="es-button action-settings" title="设置站点显示"><span class="settings-icon-symbol">⚙️</span> <span class="settings-text">设置</span></button>`;
        if (movieInfo.unititle) {
            externalLinksHTML += `<a href="https://www.themoviedb.org/search?query=${encodeURIComponent(movieInfo.unititle)}" target="_blank" class="es-button action-primary" title="在TMDB中搜索">🎬 TMDB</a>`;
            if (validateConfig()) {
                const embySearchUrl = `${embyHost}/web/index.html#!/search.html?query=${encodeURIComponent(movieInfo.unititle)}`;
                // MODIFIED: Changed Emby Search button to use 'action-secondary' for a different color
                externalLinksHTML += `<a href="${embySearchUrl}" target="_blank" class="es-button action-secondary" title="在Emby中搜索">🔍 Emby搜索</a>`;
            }
        }
        const sitesConfig = getSitesConfig();
        const ptLinks = Object.entries(sitesConfig).map(([siteKey, site]) => {
            if (!enabledSites[siteKey]) return null;
            enabledPtSiteCount++;
            let url = site.url;
            if (site.searchType === 'imdb' && movieInfo.imdb_id) url = url.replace('{imdb}', movieInfo.imdb_id);
            else if (site.searchType === 'title' && movieInfo.unititle) url = url.replace('{title}', encodeURIComponent(movieInfo.unititle));
            else if (!movieInfo.imdb_id && site.searchType === 'imdb') return null;
            else if (!movieInfo.unititle && site.searchType === 'title') return null;
            else return null;
            const styleAttribute = site.gradient ? `style="background: ${site.gradient};"` : '';
            let extraClass = '';
            const lightGradientKeywords = ['#a8edea', '#fed6e3', '#ffecd2', '#fbc2eb', '#fad0c4', '#00f2fe', '#f9f9f9', 'rgba(255,255,255'];
            if (site.gradient && lightGradientKeywords.some(keyword => site.gradient.includes(keyword))) {
                extraClass = ' light-gradient-text-shadow';
            }
            const buttonClass = site.gradient ? `es-button pt-site pt-site-item${extraClass}` : `es-button action-secondary pt-site pt-site-item${extraClass}`; // PT site buttons use action-secondary for other sites
            return `<a href="${url}" target="_blank" class="${buttonClass}" ${styleAttribute} title="在${site.name}中搜索" data-sitekey="${siteKey}" draggable="false">${site.emoji} ${site.name}</a>`;
        }).filter(Boolean);

        if (ptLinks.length > 0 || Object.keys(sitesConfig).length > 0) {
            ptLinksHTML = ptLinks.join('');
            // PT Toggle button will use its own distinct style or can be a more neutral one if needed
             ptToggleButtonHTML = `<button id="pt-toggle-btn" class="es-button" style="background: linear-gradient(135deg, #FF8C00 0%, #FFA500 100%);" title="展开/收起PT站点"><span id="toggle-icon" class="toggle-icon">▼</span> PT站点 (${enabledPtSiteCount})</button>`;
        }
        return { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML, settingsButtonHTML };
    }

    function insertUI(movieInfo) {
        document.getElementById('emby-script-container')?.remove();
        const { externalLinksHTML, ptToggleButtonHTML, ptLinksHTML, settingsButtonHTML } = generateSiteLinks(movieInfo);
        const containerHTML = `
            <div id="emby-script-container">
                <div id="emby-script-button-bar">
                    <div id="emby-status-container"></div>
                    ${externalLinksHTML}
                    ${ptToggleButtonHTML}
                    ${settingsButtonHTML}
                </div>
                <div class="pt-sites-container" id="pt-sites-container">${ptLinksHTML}</div>
            </div>`;
        const h1 = document.querySelector("#content h1");
        if (h1) {
            h1.insertAdjacentHTML('afterend', containerHTML);
        } else { document.body.insertAdjacentHTML('afterbegin', containerHTML); }

        const toggleBtn = document.getElementById('pt-toggle-btn');
        const ptContainer = document.getElementById('pt-sites-container');
        const toggleIcon = document.getElementById('toggle-icon');
        if (toggleBtn && ptContainer && toggleIcon) {
            toggleBtn.addEventListener('click', () => {
                ptContainer.classList.toggle('expanded');
                toggleIcon.classList.toggle('rotated');
            });
        }

        // 初始化PT站点拖拽排序
        if (window.Sortable && ptContainer) {
            // 销毁已存在的Sortable实例
            if (ptContainer.sortableInstance) {
                ptContainer.sortableInstance.destroy();
            }

            ptContainer.sortableInstance = Sortable.create(ptContainer, {
                animation: 150,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                forceFallback: false, // 改为false，使用原生拖拽
                fallbackTolerance: 0,
                scroll: true,
                scrollSensitivity: 30,
                scrollSpeed: 10,
                swapThreshold: 0.65,
                invertSwap: false,
                filter: 'input, button, .es-button', // 过滤掉按钮元素
                preventOnFilter: false,
                onStart: function(evt) {
                    console.log('PT站点拖拽开始:', evt.item.dataset.sitekey);
                    document.body.classList.add('dragging-active');
                    // 不禁用指针事件
                },
                onEnd: function(evt) {
                    console.log('PT站点拖拽结束:', evt.item.dataset.sitekey, '新位置:', evt.newIndex);
                    document.body.classList.remove('dragging-active');

                    // 获取新的排序并保存
                    const newOrder = Array.from(ptContainer.children)
                        .filter(item => item.dataset.sitekey) // 确保有sitekey
                        .map(item => item.dataset.sitekey);
                    console.log('PT站点新排序:', newOrder);
                    saveSiteOrder(newOrder);

                    // 显示保存成功提示
                    const saveIndicator = document.createElement('div');
                    saveIndicator.textContent = '✅ PT站点排序已保存';
                    saveIndicator.style.cssText = `
                        position: fixed; top: 20px; right: 20px; z-index: 10000;
                        background: #4caf50; color: white; padding: 8px 16px;
                        border-radius: 4px; font-size: 14px; box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                    `;
                    document.body.appendChild(saveIndicator);
                    setTimeout(() => saveIndicator.remove(), 2000);
                }
            });
        }
        const settingsBtn = document.getElementById('es-open-settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                createSettingsModal(movieInfo);
                openSettingsModal();
            });
        }
        updateSettingsButtonAppearance();
    }

    function showLoadingStatus() {
        updateStatus(`<div class="es-button status-loading"><span class="loading-spinner"></span>正在检查...</div>`);
    }

    function updateStatus(html) {
        const container = document.getElementById('emby-status-container');
        if (container) {
            container.innerHTML = html;
        }
    }

    // ==================== Emby 媒体检查 ====================
    async function searchEmbyByImdb(imdb_id) {
        const url = `${embyHost}/emby/Items?Recursive=True&AnyProviderIdEquals=imdb.${imdb_id}&api_key=${embyApiKey}`;
        const response = await fetchWithRetry(url);
        if (response?.TotalRecordCount > 0 && response.Items?.length > 0) {
            const item = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${item.Id}&serverId=${item.ServerId}`;
            return { html: `<a href="${jumpUrl}" target="_blank" class="es-button status-success" title="媒体已入库: ${item.Name || '未知标题'} (点击跳转)">✨ 已入库</a>`, status: 'found' };
        }
        return { status: 'not_found_imdb' };
    }

    async function searchEmbyByTitle(title) {
        const url = `${embyHost}/emby/Items?IncludeItemTypes=Movie,Series&Recursive=True&api_key=${embyApiKey}&SearchTerm=${encodeURIComponent(title)}`;
        const response = await fetchWithRetry(url);
        if (response?.TotalRecordCount > 0 && response.Items?.length > 0) {
            const item = response.Items[0];
            const jumpUrl = `${embyHost}/web/index.html#!/item?id=${item.Id}&serverId=${item.ServerId}`;
            return { html: `<a href="${jumpUrl}" target="_blank" class="es-button status-warning" title="疑似存在于媒体库: ${item.Name || '未知标题'} (点击跳转)">? 疑似入库</a>`, status: 'maybe_found' };
        }
        return { html: `<div class="es-button status-error" title="媒体库中暂无此影片">📭 暂未入库</div>`, status: 'not_found_final' };
    }

    async function checkEmby(movieInfo) {
        const cacheKey = `emby_script_${CONFIG.settingsKey}_${movieInfo.imdb_id || movieInfo.unititle.substring(0,10)}`;
        const cachedResult = CacheManager.get(cacheKey);
        if (cachedResult) {
            console.log("[Emby Script] Using cached result:", cachedResult);
            return cachedResult;
        }
        try {
            if (movieInfo.imdb_id) {
                const result = await searchEmbyByImdb(movieInfo.imdb_id);
                if (result.status === 'found') {
                    CacheManager.set(cacheKey, result); return result;
                }
            }
            if (movieInfo.unititle) {
                const result = await searchEmbyByTitle(movieInfo.unititle);
                CacheManager.set(cacheKey, result); return result;
            }
            const noTitleResult = { html: `<div class="es-button status-error" title="无有效标题进行搜索">❓ 无标题</div>`, status: 'no_title_for_search' };
            CacheManager.set(cacheKey, noTitleResult); return noTitleResult;
        } catch (error) {
            console.error('[Emby Script] Emby API request failed:', error.message);
            const networkErrorResult = { html: `<div class="es-button status-error" title="Emby连接失败: ${error.message}">❌ 连接失败</div>`, status: 'network_error' };
            return networkErrorResult;
        }
    }

    // ==================== 主程序入口 ====================
    async function main() {
        if (!window.location.href.includes('movie.douban.com/subject/')) return;
        loadSiteSettings();
        settingsButtonUsed = GM_getValue(CONFIG.settingsUsedKey, false);

        if (document.readyState === 'loading') {
            await new Promise(resolve => window.addEventListener('DOMContentLoaded', resolve, { once: true }));
        }
        await new Promise(resolve => setTimeout(resolve, 300));
        try {
            console.log('[Emby Script] Version', GM_info.script.version, 'Initializing...');
            injectStyles();
            const movieInfo = extractDoubanInfo();
            insertUI(movieInfo);
            if (!validateConfig()) {
                updateStatus(`<div class="es-button status-error" title="请点击设置按钮配置 Emby 服务器地址和 API 密钥">⚙️ 需要配置</div>`);
                return;
            }
            if (!movieInfo.unititle && !movieInfo.imdb_id) {
                updateStatus(`<div class="es-button status-error" title="无法从页面获取有效的影片信息">❓ 信息不足</div>`);
                return;
            }
            showLoadingStatus();
            const embyResult = await checkEmby(movieInfo);
            updateStatus(embyResult.html);
            console.log('[Emby Script] Initialization complete.');
        } catch (error) {
            console.error('[Emby Script] Main initialization error:', error);
            updateStatus(`<div class="es-button status-error" title="脚本初始化失败: ${error.message}">❌ 初始化失败</div>`);
        }
    }

    main();
})();