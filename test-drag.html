<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sortable-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .sortable-item {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            cursor: move;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }
        .sortable-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        .drag-handle {
            cursor: grab;
            color: #666;
            margin-right: 12px;
            font-size: 18px;
            user-select: none;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }
        .drag-handle:hover {
            background: rgba(0,0,0,0.1);
            color: #333;
        }
        .drag-handle:active {
            cursor: grabbing;
            background: rgba(0,0,0,0.2);
        }
        .sortable-ghost {
            opacity: 0.4 !important;
            transform: scale(0.95) !important;
            background: rgba(33, 150, 243, 0.1) !important;
            border: 2px dashed #2196f3 !important;
            pointer-events: none !important;
        }
        .sortable-chosen {
            transform: scale(1.02) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
            z-index: 1000 !important;
            opacity: 0.9 !important;
        }
        .sortable-drag {
            transform: scale(1.05) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
            z-index: 1001 !important;
            opacity: 0.8 !important;
            pointer-events: none !important;
        }
        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: #4caf50;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 拖拽功能测试</h1>
        <p>测试SortableJS拖拽功能，验证修复效果：</p>
        
        <h3>📋 测试站点列表</h3>
        <ul id="test-sites-list" class="sortable-list">
            <li class="sortable-item" data-sitekey="mteam">
                <span class="drag-handle">⋮⋮</span>
                <span>🥟 馒头</span>
            </li>
            <li class="sortable-item" data-sitekey="audiences">
                <span class="drag-handle">⋮⋮</span>
                <span>👥 观众</span>
            </li>
            <li class="sortable-item" data-sitekey="hhanclub">
                <span class="drag-handle">⋮⋮</span>
                <span>😊 憨憨</span>
            </li>
            <li class="sortable-item" data-sitekey="tjupt">
                <span class="drag-handle">⋮⋮</span>
                <span>🌟 天空</span>
            </li>
            <li class="sortable-item" data-sitekey="chdbits">
                <span class="drag-handle">⋮⋮</span>
                <span>🌈 彩虹岛</span>
            </li>
        </ul>
        
        <div>
            <button class="test-button" onclick="resetOrder()">重置顺序</button>
            <button class="test-button" onclick="showCurrentOrder()">显示当前顺序</button>
            <button class="test-button" onclick="addTestItem()">添加测试项</button>
        </div>
        
        <div id="order-display" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px; display: none;">
            <strong>当前顺序：</strong>
            <span id="order-text"></span>
        </div>
    </div>

    <script>
        let sortableInstance;
        let itemCounter = 1;

        function initSortable() {
            const sitesList = document.getElementById('test-sites-list');
            
            if (sortableInstance) {
                sortableInstance.destroy();
            }
            
            sortableInstance = Sortable.create(sitesList, {
                handle: '.drag-handle',
                animation: 150,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                forceFallback: false,
                fallbackTolerance: 0,
                scroll: true,
                scrollSensitivity: 30,
                scrollSpeed: 10,
                swapThreshold: 0.65,
                invertSwap: false,
                direction: 'vertical',
                onStart: function(evt) {
                    console.log('拖拽开始:', evt.item.dataset.sitekey);
                },
                onEnd: function(evt) {
                    console.log('拖拽结束:', evt.item.dataset.sitekey, '新位置:', evt.newIndex);
                    
                    const newOrder = Array.from(sitesList.children).map(item => item.dataset.sitekey);
                    console.log('新排序:', newOrder);
                    
                    // 显示保存成功提示
                    showSaveIndicator('✅ 排序已保存');
                    
                    // 自动显示当前顺序
                    setTimeout(() => showCurrentOrder(), 500);
                }
            });
        }

        function showSaveIndicator(message) {
            const indicator = document.createElement('div');
            indicator.textContent = message;
            indicator.className = 'save-indicator';
            document.body.appendChild(indicator);
            setTimeout(() => indicator.remove(), 2000);
        }

        function resetOrder() {
            const sitesList = document.getElementById('test-sites-list');
            const items = Array.from(sitesList.children);
            const originalOrder = ['mteam', 'audiences', 'hhanclub', 'tjupt', 'chdbits'];
            
            items.sort((a, b) => {
                const aIndex = originalOrder.indexOf(a.dataset.sitekey);
                const bIndex = originalOrder.indexOf(b.dataset.sitekey);
                return aIndex - bIndex;
            });
            
            items.forEach(item => sitesList.appendChild(item));
            showSaveIndicator('🔄 顺序已重置');
            setTimeout(() => showCurrentOrder(), 500);
        }

        function showCurrentOrder() {
            const sitesList = document.getElementById('test-sites-list');
            const order = Array.from(sitesList.children).map(item => {
                const text = item.querySelector('span:last-child').textContent;
                return text;
            });
            
            const orderDisplay = document.getElementById('order-display');
            const orderText = document.getElementById('order-text');
            orderText.textContent = order.join(' → ');
            orderDisplay.style.display = 'block';
        }

        function addTestItem() {
            const sitesList = document.getElementById('test-sites-list');
            const newItem = document.createElement('li');
            newItem.className = 'sortable-item';
            newItem.dataset.sitekey = `test_${itemCounter}`;
            newItem.innerHTML = `
                <span class="drag-handle">⋮⋮</span>
                <span>🧪 测试项 ${itemCounter}</span>
            `;
            sitesList.appendChild(newItem);
            itemCounter++;
            showSaveIndicator('➕ 已添加测试项');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSortable();
            console.log('拖拽测试页面已初始化');
        });
    </script>
</body>
</html>
