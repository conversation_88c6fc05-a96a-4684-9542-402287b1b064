<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上下移动按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .site-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            margin-bottom: 8px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
        }
        .site-toggle:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .site-controls {
            display: flex;
            flex-direction: column;
            gap: 2px;
            margin-right: 12px;
        }
        .move-btn {
            background: #2196f3;
            color: white;
            border: none;
            width: 20px;
            height: 20px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            user-select: none;
        }
        .move-btn:hover {
            background: #1976d2;
            transform: scale(1.1);
        }
        .move-btn:active {
            transform: scale(0.95);
        }
        .move-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .site-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: #4caf50;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 上下移动按钮测试</h1>
        <p>测试简化的上下移动功能，替代复杂的拖拽：</p>
        
        <h3>📋 测试站点列表</h3>
        <div id="sites-list">
            <!-- 站点列表将通过JavaScript生成 -->
        </div>
        
        <div>
            <button class="test-button" onclick="resetOrder()">重置顺序</button>
            <button class="test-button" onclick="showCurrentOrder()">显示当前顺序</button>
            <button class="test-button" onclick="addTestItem()">添加测试项</button>
        </div>
        
        <div id="order-display" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px; display: none;">
            <strong>当前顺序：</strong>
            <span id="order-text"></span>
        </div>
    </div>

    <script>
        let sites = [
            { key: 'mteam', name: '🥟 馒头', enabled: true },
            { key: 'audiences', name: '👥 观众', enabled: true },
            { key: 'hhanclub', name: '😊 憨憨', enabled: true },
            { key: 'tjupt', name: '🌟 天空', enabled: true },
            { key: 'chdbits', name: '🌈 彩虹岛', enabled: true }
        ];
        let itemCounter = 1;

        function renderSites() {
            const sitesList = document.getElementById('sites-list');
            sitesList.innerHTML = '';
            
            sites.forEach((site, index) => {
                const upBtn = index > 0 ? 
                    `<button class="move-btn move-up" data-sitekey="${site.key}" title="上移">↑</button>` : 
                    `<button class="move-btn" disabled>↑</button>`;
                
                const downBtn = index < sites.length - 1 ? 
                    `<button class="move-btn move-down" data-sitekey="${site.key}" title="下移">↓</button>` : 
                    `<button class="move-btn" disabled>↓</button>`;
                
                const siteDiv = document.createElement('div');
                siteDiv.className = 'site-toggle';
                siteDiv.dataset.sitekey = site.key;
                siteDiv.innerHTML = `
                    <div class="site-controls">
                        ${upBtn}
                        ${downBtn}
                    </div>
                    <label>${site.name}</label>
                    <div class="site-actions">
                        <input type="checkbox" ${site.enabled ? 'checked' : ''}>
                    </div>
                `;
                
                sitesList.appendChild(siteDiv);
            });
            
            // 添加事件监听器
            addEventListeners();
        }

        function addEventListeners() {
            document.querySelectorAll('.move-btn').forEach(btn => {
                btn.addEventListener('click', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    if (btn.disabled) return;
                    
                    const siteKey = event.target.dataset.sitekey;
                    const isUp = event.target.classList.contains('move-up');
                    const currentIndex = sites.findIndex(site => site.key === siteKey);
                    
                    if (currentIndex === -1) return;
                    
                    let newIndex;
                    if (isUp && currentIndex > 0) {
                        newIndex = currentIndex - 1;
                    } else if (!isUp && currentIndex < sites.length - 1) {
                        newIndex = currentIndex + 1;
                    } else {
                        return;
                    }
                    
                    // 交换位置
                    [sites[currentIndex], sites[newIndex]] = [sites[newIndex], sites[currentIndex]];
                    
                    // 重新渲染
                    renderSites();
                    
                    // 显示保存成功提示
                    const siteName = sites[newIndex].name;
                    showSaveIndicator(`✅ ${siteName} 已${isUp ? '上移' : '下移'}`);
                    
                    // 自动显示当前顺序
                    setTimeout(() => showCurrentOrder(), 500);
                });
            });
        }

        function showSaveIndicator(message) {
            const indicator = document.createElement('div');
            indicator.textContent = message;
            indicator.className = 'save-indicator';
            document.body.appendChild(indicator);
            setTimeout(() => indicator.remove(), 1500);
        }

        function resetOrder() {
            sites = [
                { key: 'mteam', name: '🥟 馒头', enabled: true },
                { key: 'audiences', name: '👥 观众', enabled: true },
                { key: 'hhanclub', name: '😊 憨憨', enabled: true },
                { key: 'tjupt', name: '🌟 天空', enabled: true },
                { key: 'chdbits', name: '🌈 彩虹岛', enabled: true }
            ];
            renderSites();
            showSaveIndicator('🔄 顺序已重置');
            setTimeout(() => showCurrentOrder(), 500);
        }

        function showCurrentOrder() {
            const order = sites.map(site => site.name);
            const orderDisplay = document.getElementById('order-display');
            const orderText = document.getElementById('order-text');
            orderText.textContent = order.join(' → ');
            orderDisplay.style.display = 'block';
        }

        function addTestItem() {
            const newSite = {
                key: `test_${itemCounter}`,
                name: `🧪 测试项 ${itemCounter}`,
                enabled: true
            };
            sites.push(newSite);
            itemCounter++;
            renderSites();
            showSaveIndicator('➕ 已添加测试项');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderSites();
            console.log('上下移动按钮测试页面已初始化');
        });
    </script>
</body>
</html>
